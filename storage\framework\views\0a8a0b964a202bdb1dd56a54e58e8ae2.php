<?php $__env->startSection('title', 'Browse Online Courses - Digitora'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="bg-gradient-to-r from-indigo-600 to-purple-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
            <h1 class="text-4xl font-bold mb-4">Learn New Skills with Expert-Led Courses</h1>
            <p class="text-xl text-indigo-100 mb-8">Discover courses designed by Indonesian entrepreneurs for Indonesian entrepreneurs</p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form method="GET" action="<?php echo e(route('browse.courses')); ?>" class="flex">
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search courses..." 
                           class="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <button type="submit" 
                            class="bg-indigo-800 hover:bg-indigo-900 px-6 py-3 rounded-r-lg font-medium transition-colors">
                        Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Results -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                
                <form method="GET" action="<?php echo e(route('browse.courses')); ?>" id="filter-form">
                    <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                    
                    <!-- Category Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <!-- Difficulty Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                        <select name="difficulty" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="">All Levels</option>
                            <option value="beginner" <?php echo e(request('difficulty') == 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                            <option value="intermediate" <?php echo e(request('difficulty') == 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                            <option value="advanced" <?php echo e(request('difficulty') == 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                        </select>
                    </div>
                    
                    <!-- Sort Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                            <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                            <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                        </select>
                    </div>
                </form>
                
                <?php if(request()->hasAny(['search', 'category', 'difficulty', 'sort'])): ?>
                    <a href="<?php echo e(route('browse.courses')); ?>" 
                       class="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Course Grid -->
        <div class="flex-1">
            <!-- Results Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">
                    <?php if(request('search')): ?>
                        Search Results for "<?php echo e(request('search')); ?>"
                    <?php else: ?>
                        All Courses
                    <?php endif; ?>
                </h2>
                <p class="text-gray-600"><?php echo e($courses->total()); ?> courses found</p>
            </div>
            
            <?php if($courses->count() > 0): ?>
                <!-- Course Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                            <!-- Course Thumbnail -->
                            <div class="aspect-video bg-gray-200 relative">
                                <?php if($course->thumbnail_url): ?>
                                    <img src="<?php echo e($course->thumbnail_url); ?>" alt="<?php echo e($course->title); ?>" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600">
                                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Difficulty Badge -->
                                <div class="absolute top-3 left-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        <?php if($course->difficulty_level == 'beginner'): ?> bg-green-100 text-green-800
                                        <?php elseif($course->difficulty_level == 'intermediate'): ?> bg-yellow-100 text-yellow-800
                                        <?php else: ?> bg-red-100 text-red-800
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst($course->difficulty_level)); ?>

                                    </span>
                                </div>
                            </div>
                            
                            <!-- Course Content -->
                            <div class="p-6">
                                <div class="mb-2">
                                    <?php if($course->detailedCategory): ?>
                                        <span class="text-xs text-indigo-600 font-medium"><?php echo e($course->detailedCategory->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="<?php echo e(route('browse.courses.show', $course)); ?>" class="hover:text-indigo-600">
                                        <?php echo e($course->title); ?>

                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($course->short_description); ?></p>
                                
                                <!-- Course Stats -->
                                <div class="flex items-center text-xs text-gray-500 mb-4 space-x-4">
                                    <span><?php echo e($course->sections_count); ?> sections</span>
                                    <span><?php echo e($course->curriculum_items_count); ?> lessons</span>
                                    <?php if($course->estimated_duration): ?>
                                        <span><?php echo e(floor($course->estimated_duration / 60)); ?>h <?php echo e($course->estimated_duration % 60); ?>m</span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Instructor -->
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-gray-600">
                                            <?php echo e(substr($course->seller->name, 0, 1)); ?>

                                        </span>
                                    </div>
                                    <span class="text-sm text-gray-700"><?php echo e($course->seller->name); ?></span>
                                </div>
                                
                                <!-- Price and Action -->
                                <div class="flex items-center justify-between">
                                    <div>
                                        <?php if($course->discount_price): ?>
                                            <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($course->discount_price, 0, ',', '.')); ?></span>
                                            <span class="text-sm text-gray-500 line-through ml-2">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                        <?php else: ?>
                                            <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <a href="<?php echo e(route('browse.courses.show', $course)); ?>" 
                                       class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        View Course
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($courses->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <!-- No Results -->
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
                    <p class="text-gray-600 mb-8">
                        We couldn't find any courses matching your criteria. Try adjusting your filters or browse all available courses.
                    </p>
                    <div class="mt-8">
                        <a href="<?php echo e(route('browse.courses')); ?>"
                           class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-base font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
                            Browse All Courses
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.browse', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/browse/courses.blade.php ENDPATH**/ ?>