<?php $__env->startSection('title', 'Browse Digital Products - Digitora'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="bg-gradient-to-r from-green-600 to-blue-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
            <h1 class="text-4xl font-bold mb-4">Discover Digital Products & E-books</h1>
            <p class="text-xl text-green-100 mb-8">Find templates, tools, and resources to grow your business</p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form method="GET" action="<?php echo e(route('browse.products')); ?>" class="flex">
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search products..." 
                           class="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <button type="submit" 
                            class="bg-green-800 hover:bg-green-900 px-6 py-3 rounded-r-lg font-medium transition-colors">
                        Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Results -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                
                <form method="GET" action="<?php echo e(route('browse.products')); ?>" id="filter-form">
                    <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                    
                    <!-- Category Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <!-- Sort Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                            <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                            <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                        </select>
                    </div>
                </form>
                
                <?php if(request()->hasAny(['search', 'category', 'sort'])): ?>
                    <a href="<?php echo e(route('browse.products')); ?>" 
                       class="inline-flex items-center text-sm text-green-600 hover:text-green-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Product Grid -->
        <div class="flex-1">
            <!-- Results Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">
                    <?php if(request('search')): ?>
                        Search Results for "<?php echo e(request('search')); ?>"
                    <?php else: ?>
                        All Products
                    <?php endif; ?>
                </h2>
                <p class="text-gray-600"><?php echo e($products->total()); ?> products found</p>
            </div>
            
            <?php if($products->count() > 0): ?>
                <!-- Product Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                            <!-- Product Image -->
                            <div class="aspect-square bg-gray-200 relative">
                                <?php if($product->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $product->image)); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-500 to-blue-600">
                                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Featured Badge -->
                                <?php if($product->is_featured): ?>
                                    <div class="absolute top-3 left-3">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                            Featured
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Product Content -->
                            <div class="p-6">
                                <div class="mb-2">
                                    <?php if($product->productDetailedCategory): ?>
                                        <span class="text-xs text-green-600 font-medium"><?php echo e($product->productDetailedCategory->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="<?php echo e(route('products.show', $product)); ?>" class="hover:text-green-600">
                                        <?php echo e($product->name); ?>

                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e(Str::limit($product->description, 100)); ?></p>
                                
                                <!-- Seller Info -->
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-gray-600">
                                            <?php echo e(substr($product->seller->name, 0, 1)); ?>

                                        </span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-700"><?php echo e($product->seller->name); ?></span>
                                        <?php if($product->seller->sellerApplication && $product->seller->sellerApplication->store_name): ?>
                                            <div class="text-xs text-gray-500"><?php echo e($product->seller->sellerApplication->store_name); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Rating -->
                                <?php if($product->average_rating > 0): ?>
                                    <div class="flex items-center mb-4">
                                        <div class="flex items-center">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <svg class="w-4 h-4 <?php echo e($i <= $product->average_rating ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="text-sm text-gray-600 ml-2">(<?php echo e($product->reviews_count); ?>)</span>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Price and Action -->
                                <div class="flex items-center justify-between">
                                    <div>
                                        <?php if($product->discount_price): ?>
                                            <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->discount_price, 0, ',', '.')); ?></span>
                                            <span class="text-sm text-gray-500 line-through ml-2">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                        <?php else: ?>
                                            <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <a href="<?php echo e(route('products.show', $product)); ?>" 
                                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        View Product
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($products->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <!-- No Results -->
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600 mb-8">
                        We couldn't find any products matching your criteria. Try adjusting your filters or browse all available products.
                    </p>
                    <div class="mt-8">
                        <a href="<?php echo e(route('browse.products')); ?>"
                           class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 transition-colors">
                            Browse All Products
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.browse', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/browse/products.blade.php ENDPATH**/ ?>